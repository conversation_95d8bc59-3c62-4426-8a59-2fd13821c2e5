# Graphiti 知识图谱演示项目

这个项目演示了如何使用 Graphiti 构建和查询时间感知的知识图谱。

## 项目结构

```
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
├── .env.example             # 环境变量示例
├── config/
│   └── database.py          # 数据库配置
├── src/
│   ├── __init__.py
│   ├── graphiti_client.py   # Graphiti客户端封装
│   ├── storage/
│   │   ├── __init__.py
│   │   └── knowledge_store.py  # 知识存储功能
│   ├── query/
│   │   ├── __init__.py
│   │   └── search_engine.py    # 查询引擎
│   └── models/
│       ├── __init__.py
│       └── custom_entities.py # 自定义实体类型
├── examples/
│   ├── __init__.py
│   ├── basic_usage.py       # 基础使用示例
│   ├── advanced_queries.py  # 高级查询示例
│   └── temporal_queries.py  # 时间感知查询示例
├── tests/
│   ├── __init__.py
│   ├── test_storage.py      # 存储功能测试
│   └── test_queries.py      # 查询功能测试
└── data/
    └── sample_data.py       # 示例数据
```

## 安装和设置

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 设置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的 OpenAI API key 和 Neo4j 连接信息
```

3. 启动 Neo4j 数据库：
```bash
# 使用 Docker 启动 Neo4j
docker run \
    --name neo4j \
    -p7474:7474 -p7687:7687 \
    -d \
    -v $HOME/neo4j/data:/data \
    -v $HOME/neo4j/logs:/logs \
    -v $HOME/neo4j/import:/var/lib/neo4j/import \
    -v $HOME/neo4j/plugins:/plugins \
    --env NEO4J_AUTH=neo4j/password \
    neo4j:latest
```

## 功能特性

### 存储功能
- 添加文本和结构化数据到知识图谱
- 自动实体识别和关系提取
- 时间戳记录和版本管理
- 批量数据导入

### 查询功能
- 语义搜索（基于向量嵌入）
- 关键词搜索（BM25）
- 图遍历查询
- 混合搜索和重排序
- 时间感知查询

### 高级功能
- 自定义实体类型
- 社区检测
- 关系强度分析
- 数据可视化

## 快速开始

```python
from src.graphiti_client import GraphitiClient

# 初始化客户端
client = GraphitiClient()

# 添加数据
await client.add_episode("张三是一名软件工程师，在北京工作")

# 查询数据
results = await client.search("软件工程师")
print(results)
```

## 示例

查看 `examples/` 目录中的示例代码：
- `basic_usage.py` - 基础使用方法
- `advanced_queries.py` - 高级查询技巧
- `temporal_queries.py` - 时间感知查询

## 测试

运行测试：
```bash
pytest tests/
```
